import traceback
import pymysql

from config import *
from logger import logger

class BaseProcessor:
    """基础处理器类，包含通用的配置和方法"""

    def __init__(self, environment):
        """初始化处理器

        Args:
            environment: 环境名称 ('test' 或 'online')
        """
        if environment not in ENVIRONMENTS:
            raise ValueError(f"不支持的环境: {environment}")

        self.environment = environment
        self.env_config = ENVIRONMENTS[environment]
        self.db_config = self.env_config["database"]

    def get_mysql_connection(self):
        """获取MySQL连接"""
        try:
            connection = pymysql.connect(
                host=self.db_config["host"],
                port=int(self.db_config["port"]),
                user=self.db_config["username"],
                password=self.db_config["password"],
                database=self.db_config["name"],
                charset='utf8mb4',
                cursorclass=pymysql.cursors.DictCursor
            )
            logger.info(f"连接到{self.environment}环境数据库成功")
            return connection, "数据库连接成功"
        except Exception as e:
            logger.error(f"数据库连接失败: {str(e)}")
            return None, f"数据库连接失败: {str(e)}"

class DataSyncProcessor(BaseProcessor):
    """数据同步处理器"""

    def query_sql(self, conn, sql):
        """Executes a SQL query and returns the result."""
        msg = ""
        try:
            with conn.cursor() as cursor:
                cursor.execute(sql)
                result = cursor.fetchall()
                msg = f"Executed query: {sql} - Rows returned: {len(result)}"
                return result, msg
        except pymysql.MySQLError as e:
            msg = f"Error executing query: {traceback.format_exc()}"
            return None, msg

    def execute_sql(self, conn, sql):
        """Executes a SQL command and returns the number of affected rows."""
        try:
            with conn.cursor() as cursor:
                cursor.execute(sql)
                conn.commit()
                msg = f"Executed command: {sql} - Rows affected: {cursor.rowcount}"
                return cursor.rowcount, msg
        except pymysql.MySQLError as e:
            msg = f"Error executing command: {traceback.format_exc()}"
            conn.rollback()
            return None, msg
        