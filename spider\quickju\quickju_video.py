import requests
import os
import asyncio
import aiohttp
import aiofiles
import tempfile
import subprocess
from urllib.parse import urljoin, urlparse
from bs4 import BeautifulSoup
from playwright.async_api import async_playwright
import logging

# 设置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class QuickVideo:
    """QuickJu视频下载器"""

    def __init__(self, timeout=30, max_retries=3):
        self.timeout = timeout
        self.max_retries = max_retries
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
        })

    def get_video_info_href(self, url):
        """
        使用requests和bs4获取video-info-footer下a标签的href

        Args:
            url: QuickJu页面URL，如 https://www.quickju.com/duanju/wonengtingjianwenwuxinsheng.html

        Returns:
            str: 视频信息页面的href链接
        """
        try:
            response = self.session.get(url, timeout=self.timeout)
            response.raise_for_status()

            soup = BeautifulSoup(response.content, 'html.parser')

            # 查找class="video-info-footer"的元素
            video_info_footer = soup.find(class_='video-info-footer')
            if not video_info_footer:
                raise ValueError("未找到class='video-info-footer'的元素")

            # 在video-info-footer中查找a标签
            a_tag = video_info_footer.find('a')
            if not a_tag:
                raise ValueError("在video-info-footer中未找到a标签")

            href = a_tag.get('href')
            if not href:
                raise ValueError("a标签没有href属性")

            # 如果href是相对路径，转换为绝对路径
            if href.startswith('/'):
                base_url = f"{urlparse(url).scheme}://{urlparse(url).netloc}"
                href = urljoin(base_url, href)
            elif not href.startswith('http'):
                href = urljoin(url, href)

            logger.info(f"获取到视频信息页面链接: {href}")
            return href

        except Exception as e:
            logger.error(f"获取视频信息页面链接失败: {str(e)}")
            raise

    async def get_video_src_with_playwright(self, href_url):
        """
        使用playwright打开href链接，获取iframe中video标签的src

        Args:
            href_url: 视频信息页面URL

        Returns:
            str: video标签的src属性值
        """
        async with async_playwright() as p:
            try:
                browser = await p.chromium.launch(headless=False)
                context = await browser.new_context(
                    user_agent='Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
                )
                page = await context.new_page()

                # 打开页面
                await page.goto(href_url, wait_until='networkidle')

                await page.pause() # 在async method等待

                # 等待iframe加载
                await page.wait_for_selector('#buffer', timeout=30000)

                # 获取iframe
                iframe_element = await page.query_selector('#buffer')
                if not iframe_element:
                    raise ValueError("未找到id='buffer'的iframe")

                # 切换到iframe内容
                iframe = await iframe_element.content_frame()
                if not iframe:
                    raise ValueError("无法获取iframe内容")

                # 在iframe中查找video标签
                await iframe.wait_for_selector('video', timeout=10000)
                video_element = await iframe.query_selector('video')
                if not video_element:
                    raise ValueError("在iframe中未找到video标签")

                # 获取video的src属性
                video_src = await video_element.get_attribute('src')
                if not video_src:
                    raise ValueError("video标签没有src属性")

                logger.info(f"获取到video src: {video_src}")

                # 执行JavaScript获取blob信息
                blob_info = await self._get_blob_info_with_js(iframe, video_src)

                await browser.close()
                return video_src, blob_info

            except Exception as e:
                logger.error(f"使用playwright获取video src失败: {str(e)}")
                if 'browser' in locals():
                    await browser.close()
                raise

    async def _get_blob_info_with_js(self, iframe, blob_url):
        """
        在playwright环境中执行JavaScript获取blob信息

        Args:
            iframe: playwright iframe对象
            blob_url: blob URL

        Returns:
            dict: blob信息
        """
        try:
            # 定义JavaScript函数
            js_code = """
            async function getBlobFromBlobUrl(url) {
                try {
                    const response = await fetch(url);
                    const blob = await response.blob();
                    console.log("获取到的 Blob 对象:", blob);
                    console.log("Blob 类型:", blob.type);
                    console.log("Blob 大小:", blob.size, "字节");

                    // 尝试读取blob内容的前几个字节来判断是否是m3u8
                    const arrayBuffer = await blob.arrayBuffer();
                    const uint8Array = new Uint8Array(arrayBuffer);
                    const textDecoder = new TextDecoder();
                    const firstBytes = textDecoder.decode(uint8Array.slice(0, 100));

                    return {
                        type: blob.type,
                        size: blob.size,
                        firstBytes: firstBytes,
                        isM3u8: firstBytes.includes('#EXTM3U') || firstBytes.includes('#EXT-X-VERSION')
                    };
                } catch (error) {
                    console.error("获取 Blob 失败:", error);
                    return { error: error.message };
                }
            }

            return await getBlobFromBlobUrl(arguments[0]);
            """

            # 执行JavaScript
            result = await iframe.evaluate(js_code, blob_url)
            logger.info(f"Blob信息: {result}")
            return result

        except Exception as e:
            logger.error(f"执行JavaScript获取blob信息失败: {str(e)}")
            return {"error": str(e)}

    async def download_m3u8_and_convert_to_mp4(self, m3u8_url, output_path, temp_dir=None):
        """
        下载m3u8文件并合并成mp4视频文件

        Args:
            m3u8_url: m3u8文件的URL
            output_path: 输出mp4文件路径
            temp_dir: 临时目录，如果为None则自动创建

        Returns:
            str: 输出文件路径
        """
        if temp_dir is None:
            temp_dir = tempfile.mkdtemp()

        try:
            # 确保输出目录存在
            os.makedirs(os.path.dirname(output_path), exist_ok=True)

            # 下载m3u8文件
            m3u8_content = await self._download_m3u8_file(m3u8_url)

            # 解析m3u8文件，获取视频片段列表
            segment_urls = self._parse_m3u8_content(m3u8_content, m3u8_url)

            if not segment_urls:
                raise ValueError("m3u8文件中没有找到视频片段")

            logger.info(f"找到 {len(segment_urls)} 个视频片段")

            # 下载所有视频片段
            segment_files = await self._download_video_segments(segment_urls, temp_dir)

            # 合并视频片段为mp4
            await self._merge_segments_to_mp4(segment_files, output_path)

            logger.info(f"视频合并完成: {output_path}")
            return output_path

        except Exception as e:
            logger.error(f"下载和转换m3u8失败: {str(e)}")
            raise
        finally:
            # 清理临时文件
            if temp_dir and os.path.exists(temp_dir):
                try:
                    import shutil
                    shutil.rmtree(temp_dir)
                    logger.info(f"清理临时目录: {temp_dir}")
                except Exception as e:
                    logger.warning(f"清理临时目录失败: {str(e)}")

    async def _download_m3u8_file(self, m3u8_url):
        """下载m3u8文件内容"""
        async with aiohttp.ClientSession() as session:
            async with session.get(m3u8_url) as response:
                response.raise_for_status()
                content = await response.text()
                logger.info(f"下载m3u8文件成功，大小: {len(content)} 字符")
                return content

    def _parse_m3u8_content(self, m3u8_content, base_url):
        """
        解析m3u8文件内容，提取视频片段URL列表

        Args:
            m3u8_content: m3u8文件内容
            base_url: m3u8文件的基础URL，用于构建相对路径

        Returns:
            list: 视频片段URL列表
        """
        segment_urls = []
        base_url_parsed = urlparse(base_url)
        base_path = f"{base_url_parsed.scheme}://{base_url_parsed.netloc}{os.path.dirname(base_url_parsed.path)}"

        for line in m3u8_content.split('\n'):
            line = line.strip()

            # 跳过注释行和空行
            if not line or line.startswith('#'):
                continue

            # 构建完整的URL
            if line.startswith('http'):
                segment_url = line
            else:
                segment_url = f"{base_path}/{line}"

            segment_urls.append(segment_url)

        return segment_urls

    async def _download_video_segments(self, segment_urls, temp_dir):
        """
        异步下载所有视频片段

        Args:
            segment_urls: 视频片段URL列表
            temp_dir: 临时目录

        Returns:
            list: 下载的片段文件路径列表
        """
        segment_files = []

        async with aiohttp.ClientSession() as session:
            tasks = []

            for i, url in enumerate(segment_urls):
                segment_file = os.path.join(temp_dir, f"segment_{i:04d}.ts")
                task = self._download_single_segment(session, url, segment_file)
                tasks.append((task, segment_file))

            # 并发下载所有片段
            for task, segment_file in tasks:
                try:
                    await task
                    segment_files.append(segment_file)
                except Exception as e:
                    logger.error(f"下载片段失败 {segment_file}: {str(e)}")
                    # 继续下载其他片段

        logger.info(f"成功下载 {len(segment_files)} 个视频片段")
        return segment_files

    async def _download_single_segment(self, session, url, filepath):
        """下载单个视频片段"""
        for attempt in range(self.max_retries):
            try:
                async with session.get(url, timeout=self.timeout) as response:
                    response.raise_for_status()

                    async with aiofiles.open(filepath, 'wb') as f:
                        async for chunk in response.content.iter_chunked(8192):
                            await f.write(chunk)

                    return

            except Exception as e:
                if attempt < self.max_retries - 1:
                    logger.warning(f"下载片段重试 {attempt + 1}/{self.max_retries}: {url}")
                    await asyncio.sleep(1)
                else:
                    raise e

    async def _merge_segments_to_mp4(self, segment_files, output_path):
        """
        使用ffmpeg合并视频片段为mp4文件

        Args:
            segment_files: 视频片段文件路径列表
            output_path: 输出mp4文件路径
        """
        if not segment_files:
            raise ValueError("没有视频片段可以合并")

        # 创建文件列表文件供ffmpeg使用
        temp_list_file = os.path.join(os.path.dirname(segment_files[0]), "filelist.txt")

        try:
            # 写入文件列表
            with open(temp_list_file, 'w', encoding='utf-8') as f:
                for segment_file in segment_files:
                    # 使用相对路径避免路径问题
                    f.write(f"file '{os.path.basename(segment_file)}'\n")

            # 构建ffmpeg命令
            cmd = [
                'ffmpeg',
                '-f', 'concat',
                '-safe', '0',
                '-i', temp_list_file,
                '-c', 'copy',
                '-y',  # 覆盖输出文件
                output_path
            ]

            # 执行ffmpeg命令
            process = await asyncio.create_subprocess_exec(
                *cmd,
                cwd=os.path.dirname(segment_files[0]),  # 设置工作目录
                stdout=asyncio.subprocess.PIPE,
                stderr=asyncio.subprocess.PIPE
            )

            _, stderr = await process.communicate()

            if process.returncode != 0:
                error_msg = stderr.decode('utf-8') if stderr else "未知错误"
                raise RuntimeError(f"ffmpeg合并失败: {error_msg}")

            logger.info(f"ffmpeg合并成功: {output_path}")

        finally:
            # 清理临时文件列表
            if os.path.exists(temp_list_file):
                os.remove(temp_list_file)

    async def process_video(self, quickju_url, output_path):
        """
        完整的视频处理流程

        Args:
            quickju_url: QuickJu页面URL
            output_path: 输出mp4文件路径

        Returns:
            str: 输出文件路径
        """
        try:
            logger.info(f"开始处理视频: {quickju_url}")

            # 步骤1: 获取视频信息页面链接
            href_url = self.get_video_info_href(quickju_url)

            # 步骤2: 使用playwright获取video src
            video_src, blob_info = await self.get_video_src_with_playwright(href_url)

            # 步骤3: 检查是否是m3u8文件
            if blob_info.get('isM3u8', False):
                logger.info("检测到m3u8格式，开始下载和转换")
                # 步骤4: 下载m3u8并转换为mp4
                result_path = await self.download_m3u8_and_convert_to_mp4(video_src, output_path)
                return result_path
            else:
                logger.warning(f"未检测到m3u8格式，blob信息: {blob_info}")
                # 如果不是m3u8，尝试直接下载
                return await self._download_direct_video(video_src, output_path)

        except Exception as e:
            logger.error(f"处理视频失败: {str(e)}")
            raise

    async def _download_direct_video(self, video_url, output_path):
        """
        直接下载视频文件

        Args:
            video_url: 视频URL
            output_path: 输出文件路径

        Returns:
            str: 输出文件路径
        """
        try:
            os.makedirs(os.path.dirname(output_path), exist_ok=True)

            async with aiohttp.ClientSession() as session:
                async with session.get(video_url) as response:
                    response.raise_for_status()

                    async with aiofiles.open(output_path, 'wb') as f:
                        async for chunk in response.content.iter_chunked(8192):
                            await f.write(chunk)

            logger.info(f"直接下载视频完成: {output_path}")
            return output_path

        except Exception as e:
            logger.error(f"直接下载视频失败: {str(e)}")
            raise


# 使用示例
async def main():
    """使用示例"""
    downloader = QuickVideo()

    # 示例URL
    url = "https://www.quickju.com/duanju/wonengtingjianwenwuxinsheng.html"
    output_path = "./downloads/video.mp4"

    try:
        result_path = await downloader.process_video(url, output_path)
        print(f"视频下载完成: {result_path}")
    except Exception as e:
        print(f"下载失败: {str(e)}")


if __name__ == "__main__":
    asyncio.run(main())