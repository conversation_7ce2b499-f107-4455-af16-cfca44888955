import requests
import os
import time
import json
import sqlite3
import traceback
import pymysql
from google import genai
from google.genai import types
from moviepy import VideoFileClip
import tempfile
import mimetypes
from image_compatibility import process_image_to_jpeg_stream

from config import *
from logger import logger

class BaseProcessor:
    """基础处理器类，包含通用的配置和方法"""

    def __init__(self, environment):
        """初始化处理器

        Args:
            environment: 环境名称 ('test' 或 'online')
        """
        if environment not in ENVIRONMENTS:
            raise ValueError(f"不支持的环境: {environment}")

        self.environment = environment
        self.env_config = ENVIRONMENTS[environment]
        self.strapi_url = self.env_config["strapi_url"]
        self.token = self.env_config["token"]
        self.db_config = self.env_config["database"]

        # AI配置
        self.gemini_api_key = GEMINI_API_KEYS.get(environment)

    def get_mysql_connection(self):
        """获取MySQL连接"""
        try:
            connection = pymysql.connect(
                host=self.db_config["host"],
                port=int(self.db_config["port"]),
                user=self.db_config["username"],
                password=self.db_config["password"],
                database=self.db_config["name"],
                charset='utf8mb4',
                cursorclass=pymysql.cursors.DictCursor
            )
            logger.info(f"连接到{self.environment}环境数据库成功")
            return connection, "数据库连接成功"
        except Exception as e:
            logger.error(f"数据库连接失败: {str(e)}")
            return None, f"数据库连接失败: {str(e)}"

    def get_strapi_headers(self, content_type="application/json"):
        """获取Strapi请求头"""
        headers = {"Authorization": f"Bearer {self.token}"}
        if content_type:
            headers["Content-Type"] = content_type
        return headers

    def append_to_output(self, new_text):
        """添加输出文本到日志"""
        logger.info(new_text)
        return new_text

class DataSyncProcessor(BaseProcessor):
    """数据同步处理器"""

    def upload_image(self, file_path, eid=None, field=None, table="api::episode.episode"):
        """上传图片到Strapi"""
        try:
            file_name, file_content = process_image_to_jpeg_stream(file_path)
            mime_type, _ = mimetypes.guess_type(file_name)

            # 检查图片是否已存在
            image_data = self.get_image(file_name)
            if len(image_data) > 0:
                msg = f"Found Image {file_name} in strapi cms backend."
                return image_data, msg

            if not mime_type:
                mime_type = 'application/octet-stream'

            files = {'files': (file_name, file_content, mime_type)}
            headers = {"Authorization": f"Bearer {self.token}"}

            data = {}
            if eid:
                data = {"ref": table, "refId": eid, "field": field}

            response = requests.post(f'{self.strapi_url}/api/upload',
                                   headers=headers, files=files, data=data)

            if response.ok:
                logger.info(f"图片上传成功: {file_name}")
                return response.json(), f"图片上传成功: {file_name}"
            else:
                logger.error(f"图片上传失败: {response.status_code}")
                return None, f"图片上传失败: {response.status_code}"

        except Exception as e:
            logger.error(f"上传图片异常: {str(e)}")
            return None, f"上传图片异常: {str(e)}"

    def get_image(self, file_name):
        """检查图片是否已存在"""
        try:
            headers = self.get_strapi_headers()
            response = requests.get(f"{self.strapi_url}/api/upload/files",
                                  headers=headers, params={"filters[name][$eq]": file_name})
            if response.ok:
                data = response.json()
                return data if data else []
            return []
        except Exception as e:
            logger.error(f"检查图片失败: {str(e)}")
            return []

    def find_episodes(self, data):
        """查找剧集"""
        try:
            headers = self.get_strapi_headers()
            params = {"filters[playlet_id][$eq]": data["playlet_id"]}
            response = requests.get(f"{self.strapi_url}/api/episodes",
                                  headers=headers, params=params)

            if response.ok:
                result = response.json()
                msg = f"Successfully found episodes for playletId {data['playlet_id']}"
                return result, msg
            else:
                msg = f"Error finding episodes for playletId {data['playlet_id']}: {response.status_code}"
                return None, msg

        except Exception as e:
            msg = f"Error finding episodes: {traceback.format_exc()}"
            return None, msg

    def create_episodes(self, data):
        """创建剧集"""
        try:
            strapi_payload = {"data": data}
            headers = self.get_strapi_headers()
            response = requests.post(f"{self.strapi_url}/api/episodes",
                                   headers=headers, json=strapi_payload)
            response.raise_for_status()
            msg = f"Successfully created episodes for playletId {data['playlet_id']}"
            return response.json(), msg

        except Exception as e:
            msg = f"Error creating episodes for playletId {data['playlet_id']}: {traceback.format_exc()}"
            return None, msg

    def update_episodes(self, document_id, data):
        """更新剧集"""
        try:
            strapi_payload = {"data": data}
            headers = self.get_strapi_headers()
            response = requests.put(f"{self.strapi_url}/api/episodes/{document_id}",
                                  headers=headers, json=strapi_payload)
            response.raise_for_status()
            msg = f"Successfully updated episodes for documentId {document_id}"
            return response.json(), msg

        except Exception as e:
            msg = f"Error updating episodes for {document_id}: {traceback.format_exc()}"
            return None, msg

    def query_sql(self, conn, sql):
        """Executes a SQL query and returns the result."""
        msg = ""
        try:
            with conn.cursor() as cursor:
                cursor.execute(sql)
                result = cursor.fetchall()
                msg = f"Executed query: {sql} - Rows returned: {len(result)}"
                return result, msg
        except pymysql.MySQLError as e:
            msg = f"Error executing query: {traceback.format_exc()}"
            return None, msg

    def execute_sql(self, conn, sql):
        """Executes a SQL command and returns the number of affected rows."""
        try:
            with conn.cursor() as cursor:
                cursor.execute(sql)
                conn.commit()
                msg = f"Executed command: {sql} - Rows affected: {cursor.rowcount}"
                return cursor.rowcount, msg
        except pymysql.MySQLError as e:
            msg = f"Error executing command: {traceback.format_exc()}"
            conn.rollback()
            return None, msg
        
    def correct_relationship(self, playlet_id, image_id):
        conn, msg = self.get_mysql_connection()
        self.append_to_output(msg)

        if conn:
            try:
                sql="select id from episodes where playlet_id = {}".format(playlet_id)
                episodes, msg = self.query_sql(conn, sql)
                if not episodes:
                    return msg 
                
                sql="select * from files_related_mph where file_id = {}".format(image_id)
                files_mph, msg = self.query_sql(conn, sql)

                relation = set()
                for mph in files_mph:
                    relation.add(f"{mph["related_type"]},{mph["field"]},{mph["order"]}")
                msgs = []                
                for row in episodes:
                    episode_id = row["id"]
                    found = False
                    for mph in files_mph:
                        if mph["related_id"] == episode_id:
                            found = True
                            break
                    if not found:
                        for rel in relation:
                            rel_arr = rel.split(",")
                            sql="insert into files_related_mph (file_id, related_id, related_type, field, `order`) values \
                            ({}, {}, '{}', '{}', {})".format(image_id, episode_id, rel_arr[0], rel_arr[1], rel_arr[2])
                            rowcnt, msg = self.execute_sql(conn, sql)
                            msgs.append(msg)
                if len(msgs) > 0:
                    return "\n".join(msgs)
                else:
                    return f"No relationships need correct for playlet_id {playlet_id} with image_id {image_id}."
            except Exception as e:
                msg = f"Error executing query: {traceback.format_exc()}"
                return msg
            finally:
                conn.close()
        else:
            return msg 
        
class DatabaseManager:
    """数据库管理器类，封装SQLite3操作"""

    def __init__(self, db_name="./data/hotdrama.db"):
        """初始化数据库管理器

        Args:
            db_name: 数据库文件名，默认使用配置中的DATABASE_NAME
        """
        self.db_name = db_name or DATABASE_NAME
        self.create_database()

    def get_connection(self):
        """获取数据库连接"""
        return sqlite3.connect(self.db_name)

    def create_database(self):
        """创建数据库和表"""
        conn = self.get_connection()
        cursor = conn.cursor()

        cursor.execute("""
            CREATE TABLE IF NOT EXISTS entity_data (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                entity_name TEXT UNIQUE,
                entity_path TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        """)

        cursor.execute("""
            CREATE TABLE IF NOT EXISTS drama_summary (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                playlet_id TEXT,
                material_id TEXT UNIQUE,
                ad_title TEXT,
                video_url TEXT,
                summary TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        """)

        conn.commit()
        conn.close()

    def check_entity_exists(self, entity_name):
        """检查实体是否已存在"""
        conn = self.get_connection()
        cursor = conn.cursor()
        cursor.execute(
            "SELECT entity_path FROM entity_data WHERE entity_name = ? ",
            (entity_name,)
        )
        result = cursor.fetchone()
        conn.close()
        return result[0] if result else None

    def save_entity_data(self, entity_name, entity_path):
        """保存实体数据"""
        conn = self.get_connection()
        cursor = conn.cursor()
        try:
            cursor.execute(
                "INSERT OR REPLACE INTO entity_data (entity_name, entity_path) VALUES (?, ?)",
                (entity_name, entity_path)
            )
            conn.commit()
            logger.info(f"保存实体数据: {entity_name} -> {entity_path}")
            return True
        except Exception as e:
            logger.error(f"保存实体数据失败: {str(e)}")
            return False
        finally:
            conn.close()

    def check_summary_exists(self, material_id):
        """检查摘要是否已存在"""
        conn = self.get_connection()
        cursor = conn.cursor()
        cursor.execute("SELECT summary FROM drama_summary WHERE material_id = ?", (material_id,))
        result = cursor.fetchone()
        conn.close()
        return result[0] if result else None

    def save_summary_data(self, playlet_id, material_id, ad_title, video_url, summary):
        """保存摘要数据"""
        conn = self.get_connection()
        cursor = conn.cursor()
        try:
            cursor.execute(
                "INSERT OR REPLACE INTO drama_summary (playlet_id, material_id, ad_title, video_url, summary) VALUES (?, ?, ?, ?, ?)",
                (playlet_id, material_id, ad_title, video_url, summary)
            )
            conn.commit()
            logger.info(f"保存摘要数据: {material_id}")
            return True
        except Exception as e:
            logger.error(f"保存摘要数据失败: {str(e)}")
            return False
        finally:
            conn.close()

class VideoSummaryProcessor(BaseProcessor):
    """视频摘要处理器"""

    def __init__(self, environment, db_manager: DatabaseManager):
        super().__init__(environment)
        self.db_manager = db_manager
        self.crawl_from_zhihu()

    def get_cached_summary(self, material_id):
        """从缓存获取摘要"""
        return self.db_manager.check_summary_exists(material_id)

    def save_summary(self, playlet_id, material_id, ad_title, video_url, summary):
        """保存摘要到缓存"""
        return self.db_manager.save_summary_data(playlet_id, material_id, ad_title, video_url, summary)

    def extract_first_five_minutes_to_buffer(self, input_path):
        """提取视频前5分钟到缓冲区"""
        try:
            video_clip = VideoFileClip(input_path)
            duration_to_extract = min(video_clip.duration, VIDEO_EXTRACT_DURATION)
            subclip = video_clip.subclipped(0, duration_to_extract)

            with tempfile.NamedTemporaryFile(suffix=".mp4", delete=False) as tmp_file:
                temp_file_path = tmp_file.name

            subclip.write_videofile(
                temp_file_path,
                codec="libx264",
                audio_codec="aac",
                logger=None
            )

            with open(temp_file_path, "rb") as f:
                video_buffer = f.read()

            os.remove(temp_file_path)
            video_clip.close()
            subclip.close()

            msg = f"Extracted first {VIDEO_EXTRACT_DURATION//60} minutes of video {input_path} to buffer."
            return video_buffer, msg

        except Exception as e:
            msg = f"Error extracting video {input_path}: {traceback.format_exc()}"
            return None, msg

    def realize_video(self, video_path, progress_callback = None):
        """使用AI分析视频并生成摘要"""
        summary = ""
        msg = ""

        try:
            if progress_callback:
                progress_callback(1, 3, desc=f"提取视频素材前5分钟到缓冲区... {video_path}")

            client = genai.Client(api_key=self.gemini_api_key)
            video_data, extract_msg = self.extract_first_five_minutes_to_buffer(video_path)
            if video_data is None:
                return summary, extract_msg

            if progress_callback:
                progress_callback(2, 3, desc=f"使用AI分析视频并生成摘要... {video_path}")

            content = types.Content(
                parts=[
                    types.Part(
                        inline_data=types.Blob(data=video_data, mime_type='video/mp4')
                    ),
                    types.Part(text=f"请将上述视频故事重写为一篇叙事的小说片段，突出故事的剧情冲突和吸引读者继续读下去的特点。限制在{SUMMARY_MAX_LENGTH}字之内。")
                ]
            )
            response = client.models.generate_content(
                model="gemini-2.5-flash", contents=content
            )
            summary = response.text

            # 使用小说样本重写内容
            if progress_callback:
                progress_callback(3, 3, desc=f"参考知乎重写故事... {video_path}")

            summary = self.rewrite_with_novel_samples(client, summary)
            msg = f"Successfully summarized video {video_path}"
            return summary, msg

        except Exception as e:
            msg = f"Error processing video {video_path}: {traceback.format_exc()}"
            return summary, msg

    def rewrite_with_novel_samples(self, client, summary):
        """使用小说样本重写内容"""
        try:
            novel_sample = ""
            with open("data/novel.json", "r", encoding="utf-8") as f:
                novel = json.load(f)
                for title, item in novel.items():
                    novel_sample += f"{title}\n{item['content']}\n\n"

            logger.info("Start to rewrite content with novel samples.")
            response = client.models.generate_content(
                model="gemini-2.5-flash",
                contents=[f"以下是目前较受欢迎的小说写法:\n{novel_sample}, 以下是一段剧情：{summary}, 从流行小说片段中挑选合适的写法,重写这一段剧情，以达到更好的戏剧化效果。直接输出改写后的内容，不要输出具体的分析细节。"]
            )
            return response.text
        except Exception as e:
            logger.error(f"Error rewriting with novel samples: {str(e)}")
            return summary  # 如果重写失败，返回原始摘要
        
    def crawl_from_zhihu(self):
        """从知乎爬取小说样本"""
        tab_types = ["hot", "recommend", "like", "read"]
        channel_types = ["male", "female"]

        headers = {
            "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/58.0.3029.110 Safari/537.3",
        }

        novel = {}
        for tab_type in tab_types:
            for channel_type in channel_types:
                r_url = f"https://www.zhihu.com/api/vip/km-indep-home-comm/billboard/list?channel_type={channel_type}&filter_key=0&limit=10&offset=0&tab_type={tab_type}"

                try:
                    response = requests.get(r_url, headers=headers)
                    response.raise_for_status()
                    data = response.json()
                    for item in data["data"]:
                        if item["title"] not in novel:
                            novel[item["title"]] = {
                                "title": item["title"],
                                "content": item["content_abstract"],
                                "artwork": item["artwork"],
                                "url": item["url"],
                                "labels": item["labels"]
                            }
                except Exception as e:
                    logger.error(f"爬取知乎数据失败: {str(e)}")

        with open("data/novel.json", "w", encoding="utf-8") as f:
            json.dump(novel, f, ensure_ascii=False, indent=4)
