#!/usr/bin/env python3
"""
QuickVideo使用示例
"""

import asyncio
import os
from quickju_video import QuickVideo

async def download_single_video():
    """下载单个视频的示例"""
    
    # 创建下载器
    downloader = QuickVideo(timeout=60, max_retries=3)
    
    # 要下载的视频URL
    url = "https://www.quickju.com/duanju/wonengtingjianwenwuxinsheng.html"
    
    # 输出目录和文件名
    output_dir = "./downloads"
    os.makedirs(output_dir, exist_ok=True)
    output_path = os.path.join(output_dir, "wonengtingjianwenwuxinsheng.mp4")
    
    try:
        print(f"开始下载视频...")
        print(f"源URL: {url}")
        print(f"输出路径: {output_path}")
        
        # 执行下载
        result_path = await downloader.process_video(url, output_path)
        
        # 检查结果
        if os.path.exists(result_path):
            file_size = os.path.getsize(result_path)
            print(f"\n✅ 下载成功!")
            print(f"文件路径: {result_path}")
            print(f"文件大小: {file_size / (1024*1024):.2f} MB")
        else:
            print("❌ 下载失败，文件不存在")
            
    except Exception as e:
        print(f"❌ 下载过程中出现错误: {str(e)}")
        import traceback
        traceback.print_exc()

async def download_multiple_videos():
    """批量下载多个视频的示例"""
    
    # 要下载的视频URL列表
    video_urls = [
        "https://www.quickju.com/duanju/wonengtingjianwenwuxinsheng.html",
        # 可以添加更多URL
    ]
    
    # 创建下载器
    downloader = QuickVideo()
    
    # 输出目录
    output_dir = "./downloads/batch"
    os.makedirs(output_dir, exist_ok=True)
    
    for i, url in enumerate(video_urls):
        try:
            print(f"\n正在下载第 {i+1}/{len(video_urls)} 个视频...")
            print(f"URL: {url}")
            
            # 生成输出文件名
            output_path = os.path.join(output_dir, f"video_{i+1:03d}.mp4")
            
            # 下载视频
            result_path = await downloader.process_video(url, output_path)
            
            if os.path.exists(result_path):
                file_size = os.path.getsize(result_path)
                print(f"✅ 第 {i+1} 个视频下载成功，大小: {file_size / (1024*1024):.2f} MB")
            else:
                print(f"❌ 第 {i+1} 个视频下载失败")
                
        except Exception as e:
            print(f"❌ 第 {i+1} 个视频下载出错: {str(e)}")
            continue  # 继续下载下一个视频

async def debug_download_process():
    """调试下载过程的示例"""
    
    downloader = QuickVideo()
    url = "https://www.quickju.com/duanju/wonengtingjianwenwuxinsheng.html"
    
    try:
        print("=== 调试模式 ===")
        
        # 步骤1: 获取视频信息页面链接
        print("\n步骤1: 获取视频信息页面链接")
        href_url = downloader.get_video_info_href(url)
        print(f"✅ 获取到链接: {href_url}")
        
        # 步骤2: 使用playwright获取视频源
        print("\n步骤2: 使用playwright获取视频源")
        video_src, blob_info = await downloader.get_video_src_with_playwright(href_url)
        print(f"✅ 视频源: {video_src}")
        print(f"✅ Blob信息: {blob_info}")
        
        # 步骤3: 检查是否为m3u8格式
        print(f"\n步骤3: 检查视频格式")
        if blob_info.get('isM3u8', False):
            print("✅ 检测到m3u8格式")
            print(f"Blob类型: {blob_info.get('type', 'unknown')}")
            print(f"Blob大小: {blob_info.get('size', 0)} 字节")
        else:
            print("ℹ️ 非m3u8格式，将尝试直接下载")
        
        # 步骤4: 执行下载（可选）
        download_choice = input("\n是否继续执行下载? (y/n): ").strip().lower()
        if download_choice == 'y':
            output_path = "./downloads/debug_video.mp4"
            os.makedirs(os.path.dirname(output_path), exist_ok=True)
            
            print(f"\n步骤4: 执行下载到 {output_path}")
            if blob_info.get('isM3u8', False):
                result_path = await downloader.download_m3u8_and_convert_to_mp4(video_src, output_path)
            else:
                result_path = await downloader._download_direct_video(video_src, output_path)
            
            if os.path.exists(result_path):
                file_size = os.path.getsize(result_path)
                print(f"✅ 下载完成: {result_path}")
                print(f"文件大小: {file_size / (1024*1024):.2f} MB")
            else:
                print("❌ 下载失败")
        
    except Exception as e:
        print(f"❌ 调试过程中出现错误: {str(e)}")
        import traceback
        traceback.print_exc()

def main():
    """主函数"""
    print("QuickVideo 使用示例")
    print("=" * 40)
    
    print("请选择示例:")
    print("1. 下载单个视频")
    print("2. 批量下载视频")
    print("3. 调试下载过程")
    
    choice = input("请输入选择 (1-3): ").strip()
    
    if choice == "1":
        asyncio.run(download_single_video())
    elif choice == "2":
        asyncio.run(download_multiple_videos())
    elif choice == "3":
        asyncio.run(debug_download_process())
    else:
        print("无效选择")

if __name__ == "__main__":
    main()
