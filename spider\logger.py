import logging
import os
from datetime import datetime
from config import LOG_FILE, LOG_MAX_LINES

class AppLogger:
    def __init__(self):
        # 确保日志目录存在
        os.makedirs(os.path.dirname(LOG_FILE), exist_ok=True)
        
        # 配置日志
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(levelname)s - %(module)s - %(message)s',
            handlers=[
                logging.FileHandler(LOG_FILE, encoding='utf-8'),
                logging.StreamHandler()
            ]
        )
        self.logger = logging.getLogger(__name__)
    
    def info(self, message):
        """记录信息日志"""
        self.logger.info(message)
    
    def error(self, message):
        """记录错误日志"""
        self.logger.error(message)
    
    def warning(self, message):
        """记录警告日志"""
        self.logger.warning(message)
    
    def get_recent_logs(self, lines=LOG_MAX_LINES):
        """获取最近的日志，模仿tail命令的行为"""
        try:
            if not os.path.exists(LOG_FILE):
                return "日志文件不存在"
            
            # 使用缓冲区大小，通常足够大以包含所需行数
            buffer_size = 8192
            with open(LOG_FILE, 'rb') as f:
                # 移动到文件末尾
                f.seek(0, os.SEEK_END)
                file_size = f.tell()
                
                # 初始化变量
                lines_found = []
                blocks = []
                block_count = 1
                
                # 从文件末尾开始读取块，直到找到足够的行或读取完整个文件
                while len(lines_found) < lines and file_size > 0:
                    # 计算读取位置
                    if file_size > buffer_size * block_count:
                        f.seek(file_size - buffer_size * block_count)
                        block_data = f.read(buffer_size)
                    else:
                        f.seek(0)
                        block_data = f.read(file_size)
                    
                    # 解码并分割行
                    try:
                        block_lines = block_data.decode('utf-8').splitlines(True)
                    except UnicodeDecodeError:
                        # 如果解码失败，尝试忽略错误
                        block_lines = block_data.decode('utf-8', errors='ignore').splitlines(True)
                    
                    # 将当前块的行添加到结果中
                    lines_found = block_lines + lines_found
                    
                    # 更新读取的块数和文件大小
                    block_count += 1
                    if file_size > buffer_size * block_count:
                        file_size = file_size - buffer_size * (block_count - 1)
                    else:
                        break
                
                # 返回最后的指定行数
                if len(lines_found) > lines:
                    lines_found = lines_found[-lines:]
                
                return ''.join(lines_found)
        except Exception as e:
            return f"读取日志失败: {str(e)}"

# 全局日志实例
logger = AppLogger()
